## “资管维” 平台 - 软件需求规格说明书 (V1.0)

### 1. 项目概述

#### 1.1 项目背景

“资管维”平台是一个专注于资产设备管理的维修与维护平台。旨在连接有设备维修需求的客户（C端用户，特别是课题组）、负责执行维修任务的工程师（服务端）以及进行统一管理和调度的平台管理员（B端），通过信息化的手段提升设备报修、维修、管理的效率与透明度。

#### 1.2 目标用户

平台主要服务于三类用户：

- **客户用户 (C-端)**: 主要为科研课题组的成员，包括负责人（PI）和普通成员。他们是设备的使用者和报修的发起者。
- **平台管理员 (B-端)**: 平台的运营和管理者，负责调度工单、管理用户、配置系统和监控数据。
- **工程师 (服务端)**: 接收工单并提供上门维修服务的专业技术人员。

------

### 2. 系统核心功能需求

我们将按用户角色来划分和描述功能模块。

#### 2.1 客户用户端 (C-端) 功能

**2.1.1 设备档案管理 (Equipment Portfolio Management)**

- 手动添加设备

  : 用户可以逐一录入名下的设备信息。

  - **必填字段**: 设备名称、维护编号 (系统应确保其唯一性)、启用日期。
  - **可选字段**: 品牌、型号、序列号、是否过保、仪器图片。

- **批量导入设备**: 支持通过模板文件（如 Excel/CSV）批量导入设备信息，简化初次建档流程。

- **设备信息查看与编辑**: 用户可以查看其名下所有设备的详细信息，并对信息进行修改。

- **设备维修历史**: 查看单一设备的完整历史维修记录（维护日志）。

- 设备二维码生成

  : 系统为每台设备生成一个唯一的二维码。

  - **权限**: 仅该设备所属课题组的PI及平台管理员可扫码查看包含设备基础信息、历史维护和维修记录的完整档案。普通用户扫码可能仅显示基础信息。

**2.1.2 报修与订单管理 (Repair & Order Management)**

- 发起报修

  :

  - **步骤一**: 选择需要报修的设备（从设备档案中选择）。
  - **步骤二**: 填写故障描述（支持文字输入，并可上传图片或短视频作为辅助说明）。
  - **步骤三**: 提交报修，生成一个待处理的工单。

- 工单管理

  :

  - **查看历史工单**: 按列表形式展示所有历史报修工单，并清晰展示工单状态。
  - **工单状态**: `待处理` `维修中` `已完成` `已取消`。
  - **操作**: 用户可以 **取消** 或 **修改** 处于 `待处理` 状态（即尚未被管理员分配给工程师）的工单。

- 服务评价

  :

  - 工单状态变为 `已完成` 后，用户可以对本次服务进行评价。
  - **评价形式**: 针对工程师的服务进行满意度选择题评价（例如：非常满意、满意、一般、不满意）。
  - **关联**: 评价结果直接与对应的工程师和工单关联。

**2.1.3 实时沟通 (Real-time Communication)**

- **在线聊天**: 在工单被工程师接收后，用户可与负责该单的工程师进行在线聊天（支持文字和语音消息）。
- **一键呼叫**: 在App内提供一个明显的“联系客服”按钮，点击可一键拨打平台客服的电话。

**2.1.4 通知与提醒 (Notifications & Alerts)**

- **推送通知**: 当工单状态发生关键变更时（如：管理员已派单、工程师已接单、工程师已出发、服务已完成等），系统会向用户App推送通知。

**2.1.5 个人账户 (My Account)**

- **信息管理**: 用户可以管理自己的个人信息，如联系电话、所属课题组等。
- **实名认证**: 新用户注册后需进行实名认证才能提交报修。

------

#### 2.2 平台管理员端 (B-端) 功能

**2.2.1 数据看板 (Dashboard)**

- 实时数据统计

  :

  - 今日新增工单量、本月累计工单量。
  - 当前待处理、维修中、已完成的工单数量。
  - 工单平均响应时间、平均完成时间。
  - 工程师接单率、好评率。

- 多维度分析

  :

  - 按时间范围（周/月/季）统计分析工单趋势。
  - 按设备类型、故障类型进行分类统计，形成可视化图表（如饼图、柱状图）。

- **数据导出**: 支持将看板数据或指定的统计报表导出为文件格式（如 Excel）。

**2.2.2 工单调度中心 (Work Order Dispatch Center)**

- **工单池**: 查看所有处于 `待处理` 状态的客户报修工单。

- 派单

  :

  - 管理员选择一个工单，并将其指派给合适的工程师。
  - 支持按工程师的技能、当前位置、负载情况等信息进行筛选，辅助派单决策。

- **紧急工单标记**: 管理员可以对某些工单标记为“紧急”，使其在列表和工程师端有更显眼的提示。

- **调度沟通**: 管理员在派单后，需要与工程师进行确认，并记录跟进状态（如：`已通知工程师` `工程师已联系客户`）。系统需记录这些跟进节点的时间。

**2.2.3 交易与结算管理 (Transaction & Billing Management)**

- 工单全生命周期管理

  : 监控和管理所有工单的状态流转。

  - **完整流程**: `客户提交(待处理)` → `管理员确认派单(待维修)` → `工程师维修(维修中)` → `客户验收(待验收)` → `发起结算(待结算)` → `财务确认(结算中)` → `客户打款(待打款)` → `确认收款(已完成)`。

- **查询功能**: 提供强大的查询功能，可根据单位、课题组、报修人、报修单号、设备信息、时间范围等一个或多个条件组合查询工单。

**2.2.4 用户与权限管理 (User & Permission Management)**

- **统一账户管理**: 管理所有三端用户（客户、管理员、工程师）的账户信息。

- **账户操作**: 包括创建账户、编辑信息、重置密码以及 **禁用/启用** 账户（逻辑删除，而非物理删除，以保留历史数据）。

- 角色与权限

  :

  - **超级管理员**: 拥有系统所有权限。
  - **子管理员**: 由超级管理员创建，并可被分配部分管理权限（例如：仅管理某个区域或某个客户群体的工单和用户）。

- **客户信息保密**: 客户的关键信息（如联系方式、具体地址）应被视为高级别敏感数据，仅在授权情况（如派单给工程师）下对特定角色可见。

**2.2.5 系统配置与资源库 (System Configuration & Resource)**

- **设备型号库**: 维护一个标准的设备品牌和型号数据库，供用户在添加设备时快速选择，以规范数据。

- 配件库存管理

  :

  - 建立配件库存表，记录配件名称、型号、当前库存量、单价等。
  - 供管理员和工程师查看。

- **公共通知管理**: 发布和管理系统公告，如节假日服务时间调整、平台升级通知等，公告将在客户登录时展示。

------

#### 2.3 工程师端 (服务端) 功能

**2.3.1 工单处理 (Work Order Processing)**

- 接收新工单

  :

  - 当管理员派单时，工程师会收到实时推送通知。
  - 通知中包含完整的工单信息：客户信息、设备信息、故障描述、图片/视频等。

- 工单状态更新

  : 工程师需在服务过程中实时更新工单状态。

  - **主要状态**: `已接单` → `已联系客户` → `已出发` → `维修中` → `待配件` → `维修完成`。

- 填写维修报告

  :

  - 维修完成后，必须填写维修报告。
  - **报告内容**: 故障根本原因分析、具体的维修过程描述、更换的配件列表（从配件库选择）。

- 客户确认签字

  :

  - 维修报告完成后，需要客户进行电子签名确认。
  - 工程师在自己的移动设备上展示报告，客户直接在屏幕上签名。签名将作为图片附加到该工单记录中。

**2.3.2 备件管理 (Spare Parts Management)**

- **配件领用**: 维修过程中如需使用配件，需在系统中记录领用，关联到具体工单，以便自动扣减库存。
- **配件申请**: 当发现所需配件库存不足或无库存时，可向管理员发起配件采购申请。

**2.3.3 个人中心 (My Center)**

- **历史工单查询**: 查看自己处理过的所有历史工单及维修报告。
- **业绩统计**: 查看自己的完单量、客户评价、收入等统计数据。
- **实名认证**: 工程师注册后需通过实名及资质认证，由管理员审核通过后方可接单。

------

### 3. 需要与客户确认的疑问点与猜测

以下是我在整理过程中，基于经验做出的猜测和发现的不明确之处，需要与您（或最终客户）进行沟通确认：

1. **用户角色细分**:
   - **猜测**: C端用户中，一个“课题组”应该有一个主要负责人（PI）和多名普通成员。PI拥有管理该课题组所有设备和查看所有工单的权限，而普通成员只能报修和查看自己提交的工单。这是常见的组织架构模式，需要确认。
   - **疑问**: 原始文档中提到“只有PI有修改权限、管理员无修改权限”，这句非常模糊且有矛盾。我的理解是：**课题组内的权限**，PI有完全修改权；而 **系统级权限**，平台管理员有最高修改权。我已按此猜测进行设计，但这需要明确。
2. **工单流程的细化**:
   - **猜测**: 原始的订单流程中，从“待确认订单”直接跳到“待维修”缺少了“派单”环节。我已在流程中补充为 `待处理 -> 待维修(已派单)`。
   - **猜测**: 从“维修费用完结需走开票报销流程”这句话来看，平台似乎需要处理支付和开票。当前的流程描述了状态，但未涉及具体的支付网关、发票系统对接等技术实现。这部分是简单的状态标记，还是需要与财务系统深度集成？
3. **二维码权限**:
   - **疑问**: “仅本课题组PI和管理员查看”，这里的“管理员”是指平台B端的管理员，还是指客户可以自己设置一个“课题组管理员”角色？我猜测是指前者，但需要确认。
4. **客户签字方式**:
   - **猜测**: “需要客户签字确认”最佳的实现方式是电子签名。如果条件不允许，次选方案是工程师打印报告单让客户签字后，拍照上传。需要确认具体实现方式。
5. **批量导入功能**:
   - **疑问**: 批量导入设备信息的功能，需要定义好标准的模板文件格式和字段规范（如 CSV 或 Excel）。
6. **“高级保密”的具体要求**:
   - **疑问**: “客户信息管理‘高级保密’”是一个非功能性需求。需要明确其具体要求，例如：数据是否需要加密存储？哪些字段需要脱敏显示（如电话号码显示为 `138****1234`）？访问日志是否需要被记录？
7. **“回访”流程**:
   - **疑问**: 管理员B端的“回访”指的是什么？是指派单后，电话确认工程师是否已出发或联系客户吗？我将其理解为一种“调度跟进”动作，并体现在了系统中，但这需要确认其具体业务流程。





------



好的，遵照您的要求。

下面是为“资管维”平台设计的数据库结构。此设计遵循了标准的数据库范式，具备良好的扩展性，并针对之前提到的不明确点给出了具体的、可落地的实现方案。

### 数据库设计（ER图逻辑）

&lt;br>

&lt;img src="https://i.imgur.com/7bQeU41.png" alt="资管维平台数据库ER图" width="800"/>

&lt;br>

------

### 核心表结构 (SQL DDL)

我们将数据库分为几个核心模块：**用户与权限**、**组织与设备**、**工单与流程**、**备件与库存**以及**系统与支持**。

#### 1. 用户与权限模块 (Users & Permissions)

这个模块采用灵活的 **RBAC (Role-Based Access Control)** 模型，可以轻松应对未来权限的变更。

SQL

```
-- 1.1 角色表 (Roles)
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称 (SuperAdmin, SubAdmin, PI, User, Engineer)'
);

-- 1.2 用户表 (Users) -- 存储所有三端用户的核心信息
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '登录用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '加密后的密码',
    full_name VARCHAR(100) NOT NULL COMMENT '用户真实姓名 (实名制)',
    phone_number VARCHAR(20) UNIQUE COMMENT '手机号',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    role_id INT NOT NULL COMMENT '外键, 关联角色',
    research_group_id INT COMMENT '外键, 关联所属课题组 (客户用户)',
    is_active BOOLEAN DEFAULT true COMMENT '账户是否激活/禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL COMMENT '软删除标记',
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (research_group_id) REFERENCES research_groups(id)
);

-- 1.3 权限细节 (可预留，用于更细粒度的控制，例如子管理员)
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '被授权的用户 (特指子管理员)',
    managed_group_id INT COMMENT '被授权管理的课题组ID',
    -- can_edit_users BOOLEAN DEFAULT false,
    -- can_dispatch_orders BOOLEAN DEFAULT false,
    -- ... 其他细分权限
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (managed_group_id) REFERENCES research_groups(id)
);
```

------

#### 2. 组织与设备模块 (Organizations & Equipment)

这个模块定义了客户的组织架构以及他们所拥有的设备。

SQL

```
-- 2.1 组织/单位表 (Organizations) -- 课题组的上一级单位，如“XX大学”
CREATE TABLE organizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE COMMENT '单位名称'
);


-- 2.2 课题组表 (Research Groups)
CREATE TABLE research_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '课题组名称',
    organization_id INT COMMENT '所属单位',
    pi_user_id INT COMMENT '该组的PI负责人 (关联用户表)',
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    FOREIGN KEY (pi_user_id) REFERENCES users(id)
);

-- 2.3 设备品牌库 (Equipment Brands)
CREATE TABLE equipment_brands (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '品牌名称'
);

-- 2.4 设备型号库 (Equipment Models)
CREATE TABLE equipment_models (
    id INT PRIMARY KEY AUTO_INCREMENT,
    brand_id INT NOT NULL,
    name VARCHAR(100) NOT NULL COMMENT '型号名称',
    UNIQUE (brand_id, name),
    FOREIGN KEY (brand_id) REFERENCES equipment_brands(id)
);


-- 2.5 设备档案表 (Equipment)
CREATE TABLE equipment (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '设备自定义名称',
    maintenance_code VARCHAR(100) NOT NULL UNIQUE COMMENT '维护编号 (唯一)',
    qr_code_url VARCHAR(255) COMMENT '二维码图片URL',
    research_group_id INT NOT NULL COMMENT '所属课题组',
    model_id INT COMMENT '外键, 关联标准型号库',
    serial_number VARCHAR(255) COMMENT '序列号',
    purchase_date DATE COMMENT '启用/购买日期',
    is_under_warranty BOOLEAN COMMENT '是否过保',
    custom_fields JSON COMMENT '预留自定义字段 (JSON格式)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL COMMENT '软删除标记',
    FOREIGN KEY (research_group_id) REFERENCES research_groups(id),
    FOREIGN KEY (model_id) REFERENCES equipment_models(id)
);
```

------

#### 3. 工单与流程模块 (Work Orders & Process)

这是平台业务的核心，记录了从报修到完成的全过程。

SQL

```
-- 3.1 工单表 (Work Orders)
CREATE TABLE work_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '工单号 (系统生成)',
    equipment_id INT NOT NULL COMMENT '报修的设备',
    created_by_user_id INT NOT NULL COMMENT '报修人',
    assigned_to_engineer_id INT COMMENT '被指派的工程师',
    fault_description TEXT NOT NULL COMMENT '故障现象描述',
    status ENUM(
        'Pending Confirmation', -- 待处理/待确认
        'Pending Dispatch',     -- 待派单 (猜测补充)
        'Pending Service',      -- 待维修 (已派单)
        'In Progress',          -- 维修中
        'Pending Parts',        -- 待配件
        'Pending Acceptance',   -- 待验收 (维修完成)
        'Pending Billing',      -- 待结算
        'Billing In Progress',  -- 结算中
        'Completed',            -- 已完成
        'Cancelled'             -- 已取消
    ) NOT NULL DEFAULT 'Pending Confirmation',
    is_urgent BOOLEAN DEFAULT false COMMENT '是否为紧急工单',
    repair_report TEXT COMMENT '维修报告内容',
    customer_signature_url VARCHAR(255) COMMENT '客户电子签名图片URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL COMMENT '工单完成时间',
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    FOREIGN KEY (created_by_user_id) REFERENCES users(id),
    FOREIGN KEY (assigned_to_engineer_id) REFERENCES users(id)
);

-- 3.2 工单更新日志 (Work Order Updates) -- 追踪所有状态变更和沟通记录
CREATE TABLE work_order_updates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    work_order_id INT NOT NULL,
    user_id INT COMMENT '操作人',
    update_type VARCHAR(50) NOT NULL COMMENT '更新类型 (StatusChange, Comment)',
    previous_status VARCHAR(50),
    new_status VARCHAR(50),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 3.3 服务评价表 (Service Evaluations)
CREATE TABLE service_evaluations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    work_order_id INT NOT NULL UNIQUE,
    rating INT NOT NULL COMMENT '评分 (例如 1-4)',
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (work_order_id) REFERENCES work_orders(id)
);
```

------

#### 4. 备件与库存模块 (Parts & Inventory)

SQL

```
-- 4.1 备件/配件表 (Parts)
CREATE TABLE parts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_number VARCHAR(100) UNIQUE COMMENT '配件料号',
    name VARCHAR(255) NOT NULL,
    description TEXT,
    stock_quantity INT NOT NULL DEFAULT 0,
    unit_price DECIMAL(10, 2)
);

-- 4.2 工单使用配件记录 (Work Order Parts Usage)
CREATE TABLE work_order_parts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    work_order_id INT NOT NULL,
    part_id INT NOT NULL,
    quantity_used INT NOT NULL,
    logged_by_engineer_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
    FOREIGN KEY (part_id) REFERENCES parts(id),
    FOREIGN KEY (logged_by_engineer_id) REFERENCES users(id)
);

-- 4.3 配件采购申请 (Part Requests)
CREATE TABLE part_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_id INT NOT NULL,
    quantity_requested INT NOT NULL,
    requested_by_engineer_id INT NOT NULL,
    status ENUM('Pending', 'Approved', 'Rejected', 'Ordered') NOT NULL DEFAULT 'Pending',
    approved_by_admin_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (part_id) REFERENCES parts(id),
    FOREIGN KEY (requested_by_engineer_id) REFERENCES users(id),
    FOREIGN KEY (approved_by_admin_id) REFERENCES users(id)
);
```

------

#### 5. 系统与支持模块 (System & Support)

存放附件、通知、聊天等通用功能。

SQL

```
-- 5.1 附件表 (Attachments) -- 通用附件表，可关联到任何其他表
CREATE TABLE attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_type VARCHAR(50),
    attachable_id INT NOT NULL COMMENT '关联对象的ID',
    attachable_type VARCHAR(50) NOT NULL COMMENT '关联对象的类型 (e.g., Equipment, WorkOrder)',
    uploaded_by_user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by_user_id) REFERENCES users(id)
);

-- 5.2 实时聊天消息 (Chat Messages)
CREATE TABLE chat_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    work_order_id INT NOT NULL,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message_type ENUM('text', 'voice', 'image') NOT NULL DEFAULT 'text',
    content TEXT NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (receiver_id) REFERENCES users(id)
);

-- 5.3 系统公告 (System Announcements)
CREATE TABLE system_announcements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    created_by_admin_id INT NOT NULL,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_admin_id) REFERENCES users(id)
);
```

### 设计说明与变更预留

1. **软删除（Soft Deletes）**：关键表（如 `users`, `equipment`）都加入了 `deleted_at` 字段。这允许“禁用”用户或“删除”设备而不丢失任何关联的历史数据，满足了需求中的“删除权限（禁用客户或者工程师等）”。
2. **角色权限的灵活性**：通过 `roles` 表，可以轻松添加新的用户角色。`user_permissions` 表的设计更是为了应对未来可能出现的“子管理员只能管理特定课题组”这类细粒度权限需求。
3. **JSON 字段预留**：在 `equipment` 表中加入了 `custom_fields` 字段（JSON类型）。未来如果客户需要为特定设备添加一些非标准属性（如“资产编号”、“存放位置”），无需修改表结构，直接通过程序写入JSON数据即可。
4. **通用附件表**：使用 `attachments` 表和多态关联（`attachable_id`, `attachable_type`）的设计，无论是设备图片、故障照片还是维修报告扫描件，都可以存放在同一张表中，极大地增强了系统的附件处理能力和扩展性。
5. **清晰的工单状态**：`work_orders` 表中的 `status` 字段使用了 `ENUM` 类型，严格定义了工单流转的每一个状态，使得业务逻辑更清晰、可控。我已根据猜测将完整的生命周期状态都包含在内。