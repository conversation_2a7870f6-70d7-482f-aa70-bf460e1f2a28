// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    refreshToken: null,
    currentRole: 'user',
    roles: [],
    isMultiRole: false,
    baseUrl: 'https://api.weixin.qq.com/',
    // 开发环境
    // baseUrl: 'http://localhost:3000/api'

    // 开发模式标志 - 使用假数据
    isDevelopment: true
  },

  onLaunch() {
    console.log('小程序启动');
    this.checkLoginStatus();
  },

  onShow() {
    console.log('小程序显示');
  },

  onHide() {
    console.log('小程序隐藏');
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      this.globalData.currentRole = userInfo.currentRole || 'user';
      this.globalData.roles = userInfo.roles || ['user'];
      this.globalData.isMultiRole = userInfo.isMultiRole || false;
    }
  },

  // 微信登录
  async wechatLogin(userProfile) {
    try {
      // 获取微信授权码
      const loginRes = await this.wxLogin();

      // 调用后端登录接口
      const loginResult = await this.request({
        url: '/auth/wechat-login',
        method: 'POST',
        data: {
          code: loginRes.code,
          userInfo: userProfile.userInfo
        }
      });

      if (loginResult.success) {
        const { user, token, refreshToken, needRoleSelection } = loginResult.data;
        
        // 保存登录信息
        this.globalData.userInfo = user;
        this.globalData.token = token;
        this.globalData.refreshToken = refreshToken;
        this.globalData.currentRole = user.currentRole;
        this.globalData.roles = user.roles;
        this.globalData.isMultiRole = user.isMultiRole;

        // 本地存储
        wx.setStorageSync('token', token);
        wx.setStorageSync('refreshToken', refreshToken);
        wx.setStorageSync('userInfo', user);

        // 如果需要角色选择，跳转到角色选择页面
        if (needRoleSelection) {
          wx.navigateTo({
            url: '/pages/role-select/role-select'
          });
        }

        return { success: true, needRoleSelection };
      } else {
        throw new Error(loginResult.message);
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
      return { success: false, error: error.message };
    }
  },

  // 微信登录
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },



  // 切换角色
  async switchRole(targetRole) {
    try {
      const result = await this.request({
        url: '/roles/switch',
        method: 'POST',
        data: { targetRole }
      });

      if (result.success) {
        const { currentRole, token } = result.data;
        
        // 更新全局数据
        this.globalData.currentRole = currentRole;
        this.globalData.token = token;
        
        // 更新用户信息
        const userInfo = { ...this.globalData.userInfo, currentRole };
        this.globalData.userInfo = userInfo;

        // 更新本地存储
        wx.setStorageSync('token', token);
        wx.setStorageSync('userInfo', userInfo);

        return { success: true };
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('角色切换失败:', error);
      wx.showToast({
        title: '角色切换失败',
        icon: 'error'
      });
      return { success: false, error: error.message };
    }
  },

  // 登出
  logout() {
    this.globalData.userInfo = null;
    this.globalData.token = null;
    this.globalData.refreshToken = null;
    this.globalData.currentRole = 'user';
    this.globalData.roles = [];
    this.globalData.isMultiRole = false;

    wx.removeStorageSync('token');
    wx.removeStorageSync('refreshToken');
    wx.removeStorageSync('userInfo');

    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      const { url, method = 'GET', data = {}, needAuth = true } = options;

      // 开发模式：使用假数据
      if (this.globalData.isDevelopment) {
        setTimeout(() => {
          const mockResponse = this.getMockResponse(url, method, data);
          resolve(mockResponse);
        }, 500); // 模拟网络延迟
        return;
      }

      const header = {
        'Content-Type': 'application/json'
      };

      // 添加认证头
      if (needAuth && this.globalData.token) {
        header.Authorization = `Bearer ${this.globalData.token}`;
      }

      wx.request({
        url: this.globalData.baseUrl + url,
        method,
        data,
        header,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else if (res.statusCode === 401) {
            // Token过期，尝试刷新
            this.refreshToken().then(() => {
              // 重新发起请求
              this.request(options).then(resolve).catch(reject);
            }).catch(() => {
              // 刷新失败，跳转登录
              this.logout();
              reject(new Error('登录已过期'));
            });
          } else {
            reject(new Error(res.data.message || '请求失败'));
          }
        },
        fail: (error) => {
          console.error('请求失败:', error);
          reject(error);
        }
      });
    });
  },

  // 模拟API响应
  getMockResponse(url, method, data) {
    console.log('Mock API:', method, url, data);

    // 登录接口
    if (url === '/auth/wechat-login' && method === 'POST') {
      return {
        success: true,
        message: '登录成功',
        data: {
          user: {
            id: 1,
            username: data.userInfo?.nickName || '测试用户',
            avatar: data.userInfo?.avatarUrl || '/images/default-avatar.png',
            phone: '13800138000',
            email: '<EMAIL>',
            isVerified: true,
            currentRole: 'user',
            roles: ['user', 'pi'],
            isMultiRole: true,
            department: '计算机学院',
            researchGroup: '人工智能实验室'
          },
          token: 'mock_token_' + Date.now(),
          refreshToken: 'mock_refresh_token_' + Date.now(),
          needRoleSelection: true
        }
      };
    }

    // 角色切换接口
    if (url === '/roles/switch' && method === 'POST') {
      return {
        success: true,
        message: '角色切换成功',
        data: {
          currentRole: data.targetRole,
          token: 'mock_token_' + Date.now()
        }
      };
    }

    // 获取用户角色列表
    if (url === '/roles/my-roles' && method === 'GET') {
      return {
        success: true,
        data: {
          roles: [
            {
              role: 'user',
              roleName: '普通用户',
              department: '计算机学院',
              isActive: true
            },
            {
              role: 'pi',
              roleName: 'PI负责人',
              department: '计算机学院',
              isActive: true
            },
            {
              role: 'engineer',
              roleName: '维修工程师',
              department: '设备维修部',
              isActive: false
            }
          ],
          currentRole: this.globalData.currentRole
        }
      };
    }

    // 刷新Token接口
    if (url === '/auth/refresh-token' && method === 'POST') {
      return {
        success: true,
        message: 'Token刷新成功',
        data: {
          token: 'mock_token_' + Date.now(),
          refreshToken: 'mock_refresh_token_' + Date.now()
        }
      };
    }

    // 首页统计数据
    if ((url === '/users/stats' || url === '/engineers/stats') && method === 'GET') {
      return {
        success: true,
        data: {
          equipmentCount: 25,
          orderCount: 12,
          pendingOrders: 3,
          completedOrders: 8,
          assignedOrders: 5,
          inProgressOrders: 2,
          completedToday: 1
        }
      };
    }

    // 工单列表
    if ((url === '/orders/my' || url === '/orders/assigned' || url === '/orders') && method === 'GET') {
      const mockOrders = [
        {
          id: 1,
          orderNumber: 'WO202401001',
          title: '激光打印机维修',
          status: 'pending',
          priority: 'high',
          createdAt: '2024-01-15 10:30:00',
          equipment: { name: 'HP LaserJet Pro', model: 'M404n' },
          creator: { username: '张三' }
        },
        {
          id: 2,
          orderNumber: 'WO202401002',
          title: '显微镜校准',
          status: 'in_progress',
          priority: 'medium',
          createdAt: '2024-01-14 14:20:00',
          equipment: { name: '光学显微镜', model: 'BX53' },
          creator: { username: '李四' }
        }
      ];

      return {
        success: true,
        data: {
          list: mockOrders,
          pagination: { totalPages: 1, currentPage: 1, total: 2 },
          stats: { total: 2, pending: 1, in_progress: 1, completed: 0 }
        }
      };
    }

    // 创建工单
    if (url === '/orders' && method === 'POST') {
      return {
        success: true,
        message: '工单创建成功',
        data: {
          id: Date.now(),
          orderNumber: 'WO' + new Date().getFullYear() +
                      String(new Date().getMonth() + 1).padStart(2, '0') +
                      String(Date.now()).slice(-3),
          ...data,
          status: 'pending',
          createdAt: new Date().toISOString()
        }
      };
    }

    // 工单详情
    if (url.startsWith('/orders/') && method === 'GET' && !url.includes('/history')) {
      const orderId = url.split('/')[2];
      return {
        success: true,
        data: {
          id: orderId,
          orderNumber: 'WO202401001',
          title: '激光打印机卡纸故障',
          description: '打印机经常卡纸，特别是打印双面文档时。已经尝试清理纸道，但问题依然存在。影响日常办公效率。',
          status: 'in_progress',
          priority: 'high',
          faultType: 'hardware',
          urgentLevel: 'urgent',
          contactPhone: '13800138000',
          expectedDate: '2024-01-20',
          createdAt: '2024-01-15 10:30:00',
          equipment: {
            id: 1,
            name: 'HP LaserJet Pro',
            model: 'M404n',
            location: '实验室A-101'
          },
          creator: {
            id: 1,
            username: '张三'
          },
          assignee: {
            id: 2,
            username: '李工程师'
          },
          images: [
            '/images/fault1.jpg',
            '/images/fault2.jpg'
          ]
        }
      };
    }

    // 工单状态历史
    if (url.includes('/orders/') && url.includes('/history') && method === 'GET') {
      return {
        success: true,
        data: {
          list: [
            {
              id: 1,
              action: '工单已创建',
              operator: '张三',
              createdAt: '2024-01-15 10:30:00',
              remark: '用户提交维修申请'
            },
            {
              id: 2,
              action: '工单已接受',
              operator: '李工程师',
              createdAt: '2024-01-15 14:20:00',
              remark: '已安排维修人员'
            },
            {
              id: 3,
              action: '开始处理',
              operator: '李工程师',
              createdAt: '2024-01-16 09:00:00',
              remark: '现场检查设备状况'
            }
          ]
        }
      };
    }

    // 更新工单状态
    if (url.includes('/orders/') && url.includes('/status') && method === 'PUT') {
      return {
        success: true,
        message: '状态更新成功',
        data: {
          status: data.status,
          updatedAt: new Date().toISOString()
        }
      };
    }

    // 设备列表
    if (url === '/equipment' && method === 'GET') {
      const allEquipment = [
        {
          id: 1,
          name: 'HP LaserJet Pro',
          model: 'M404n',
          serialNumber: 'HP001234',
          maintenanceCode: 'EQ001',
          brand: 'HP',
          status: 'normal',
          location: '实验室A-101',
          purchaseDate: '2023-06-15',
          warrantyExpiry: '2025-06-15',
          updatedAt: '2024-01-15 14:30:00'
        },
        {
          id: 2,
          name: '光学显微镜',
          model: 'BX53',
          serialNumber: 'OLY5678',
          maintenanceCode: 'EQ002',
          brand: 'Olympus',
          status: 'maintenance',
          location: '实验室B-203',
          purchaseDate: '2022-03-20',
          warrantyExpiry: '2024-03-20',
          updatedAt: '2024-01-14 09:15:00'
        },
        {
          id: 3,
          name: '高速离心机',
          model: 'CR22N',
          serialNumber: 'HT9876',
          maintenanceCode: 'EQ003',
          brand: 'Hitachi',
          status: 'fault',
          location: '实验室C-305',
          purchaseDate: '2021-11-10',
          warrantyExpiry: '2023-11-10',
          updatedAt: '2024-01-13 16:45:00'
        },
        {
          id: 4,
          name: '电子天平',
          model: 'XS205',
          serialNumber: 'MT2468',
          maintenanceCode: 'EQ004',
          brand: 'Mettler Toledo',
          status: 'normal',
          location: '实验室A-102',
          purchaseDate: '2023-08-20',
          warrantyExpiry: '2025-08-20',
          updatedAt: '2024-01-12 11:20:00'
        },
        {
          id: 5,
          name: 'PCR仪',
          model: 'T100',
          serialNumber: 'BR1357',
          maintenanceCode: 'EQ005',
          brand: 'Bio-Rad',
          status: 'normal',
          location: '实验室D-401',
          purchaseDate: '2022-05-15',
          warrantyExpiry: '2024-05-15',
          updatedAt: '2024-01-11 08:30:00'
        }
      ];

      // 根据查询参数过滤
      let filteredEquipment = allEquipment;

      // 状态过滤
      if (data.status) {
        filteredEquipment = filteredEquipment.filter(item => item.status === data.status);
      }

      // 关键词搜索
      if (data.keyword) {
        const keyword = data.keyword.toLowerCase();
        filteredEquipment = filteredEquipment.filter(item =>
          item.name.toLowerCase().includes(keyword) ||
          item.model.toLowerCase().includes(keyword) ||
          item.serialNumber.toLowerCase().includes(keyword) ||
          item.maintenanceCode.toLowerCase().includes(keyword)
        );
      }

      // 分页
      const page = parseInt(data.page) || 1;
      const limit = parseInt(data.limit) || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedList = filteredEquipment.slice(startIndex, endIndex);

      // 统计数据
      const stats = {
        total: allEquipment.length,
        normal: allEquipment.filter(item => item.status === 'normal').length,
        maintenance: allEquipment.filter(item => item.status === 'maintenance').length,
        fault: allEquipment.filter(item => item.status === 'fault').length,
        retired: allEquipment.filter(item => item.status === 'retired').length
      };

      return {
        success: true,
        data: {
          list: paginatedList,
          pagination: {
            totalPages: Math.ceil(filteredEquipment.length / limit),
            currentPage: page,
            total: filteredEquipment.length
          },
          stats
        }
      };
    }

    // 添加设备
    if (url === '/equipment' && method === 'POST') {
      return {
        success: true,
        message: '设备添加成功',
        data: {
          id: Date.now(),
          ...data,
          status: 'normal',
          createdAt: new Date().toISOString()
        }
      };
    }

    // 设备详情
    if (url.startsWith('/equipment/') && method === 'GET' && !url.includes('/orders')) {
      const equipmentId = url.split('/')[2];
      return {
        success: true,
        data: {
          id: equipmentId,
          name: 'HP LaserJet Pro',
          model: 'M404n',
          serialNumber: 'HP001234',
          manufacturer: 'HP Inc.',
          status: 'normal',
          location: '实验室A-101',
          department: '计算机学院',
          researchGroup: '人工智能实验室',
          category: 'computer',
          purchaseDate: '2023-06-15',
          warrantyExpiry: '2025-06-15',
          price: '3500',
          supplier: '北京科技有限公司',
          description: '高性能激光打印机，适用于办公文档打印',
          specifications: '打印速度：38页/分钟\n分辨率：1200x1200dpi\n内存：256MB\n接口：USB 2.0, 以太网',
          createdAt: '2023-06-15 10:30:00'
        }
      };
    }

    // 设备维修历史
    if (url.includes('/equipment/') && url.includes('/orders') && method === 'GET') {
      return {
        success: true,
        data: {
          list: [
            {
              id: 1,
              orderNumber: 'WO202401001',
              title: '打印机卡纸维修',
              status: 'completed',
              statusText: '已完成',
              createdAt: '2024-01-10 14:30:00'
            },
            {
              id: 2,
              orderNumber: 'WO202312015',
              title: '定期保养',
              status: 'completed',
              statusText: '已完成',
              createdAt: '2023-12-15 09:00:00'
            }
          ]
        }
      };
    }

    // 公告列表
    if (url === '/announcements' && method === 'GET') {
      const mockAnnouncements = [
        {
          id: 1,
          title: '系统维护通知',
          content: '系统将于本周六进行维护升级',
          createdAt: '2024-01-15 09:00:00',
          isImportant: true
        },
        {
          id: 2,
          title: '新功能上线',
          content: '设备管理模块新增批量导入功能',
          createdAt: '2024-01-14 16:30:00',
          isImportant: false
        }
      ];

      return {
        success: true,
        data: {
          list: mockAnnouncements,
          pagination: { totalPages: 1, currentPage: 1, total: 2 }
        }
      };
    }

    // 获取用户信息
    if (url === '/users/profile' && method === 'GET') {
      return {
        success: true,
        data: {
          id: 1,
          username: '张三',
          phone: '13800138000',
          email: '<EMAIL>',
          avatar: '/images/default-avatar.png',
          department: '计算机学院',
          researchGroup: '人工智能实验室',
          isVerified: false,
          joinDate: '2023-09-01',
          lastLoginAt: '2024-01-15 14:30:00'
        }
      };
    }

    // 更新用户信息
    if (url === '/users/profile' && method === 'PUT') {
      return {
        success: true,
        message: '信息更新成功',
        data: {
          ...data,
          updatedAt: new Date().toISOString()
        }
      };
    }

    // 获取通知数量
    if (url === '/notifications/unread-count' && method === 'GET') {
      return {
        success: true,
        data: {
          count: Math.floor(Math.random() * 10) // 随机生成0-9的未读数量
        }
      };
    }

    // 获取认证状态
    if (url === '/users/verify-status' && method === 'GET') {
      return {
        success: true,
        data: {
          isVerified: false,
          status: 'pending',
          realName: '',
          idCard: ''
        }
      };
    }

    // 提交实名认证
    if (url === '/users/verify' && method === 'POST') {
      return {
        success: true,
        message: '认证申请提交成功',
        data: {
          status: 'processing',
          submittedAt: new Date().toISOString()
        }
      };
    }

    // 消息列表（聊天页面）
    if (url === '/messages' && method === 'GET') {
      return {
        success: true,
        data: {
          list: [
            {
              id: 1,
              content: '您好，欢迎使用资管维系统！',
              type: 'text',
              sender: 'system',
              senderName: '系统助手',
              avatar: '/images/system-avatar.png',
              createdAt: '2024-01-15 09:00:00'
            },
            {
              id: 2,
              content: '如果您在使用过程中遇到任何问题，可以随时联系我们。',
              type: 'text',
              sender: 'system',
              senderName: '系统助手',
              avatar: '/images/system-avatar.png',
              createdAt: '2024-01-15 09:01:00'
            },
            {
              id: 3,
              content: '您可以通过以下方式获得帮助：\n1. 查看帮助文档\n2. 联系技术支持\n3. 提交反馈建议',
              type: 'text',
              sender: 'system',
              senderName: '系统助手',
              avatar: '/images/system-avatar.png',
              createdAt: '2024-01-15 09:02:00'
            }
          ]
        }
      };
    }

    // 发送消息
    if (url === '/messages' && method === 'POST') {
      // 模拟自动回复
      const autoReplies = [
        '收到您的消息，我们会尽快回复。',
        '感谢您的反馈，我们正在处理中。',
        '如需紧急帮助，请拨打客服热线：400-123-4567',
        '您的问题已记录，技术人员会在24小时内联系您。'
      ];

      const userMessage = {
        id: Date.now(),
        content: data.content,
        type: data.type,
        sender: 'user',
        senderName: '我',
        avatar: '/images/default-avatar.png',
        createdAt: new Date().toISOString()
      };

      // 模拟系统自动回复（延迟1秒）
      setTimeout(() => {
        const replyContent = autoReplies[Math.floor(Math.random() * autoReplies.length)];
        // 这里在实际应用中会通过WebSocket或其他方式推送消息
        console.log('Auto reply:', replyContent);
      }, 1000);

      return {
        success: true,
        data: userMessage
      };
    }

    // 默认响应
    return {
      success: false,
      message: '接口未实现',
      data: null
    };
  },

  // 刷新Token
  async refreshToken() {
    try {
      const result = await this.request({
        url: '/auth/refresh-token',
        method: 'POST',
        data: {
          refreshToken: this.globalData.refreshToken
        },
        needAuth: false
      });

      if (result.success) {
        const { token, refreshToken } = result.data;
        this.globalData.token = token;
        this.globalData.refreshToken = refreshToken;

        wx.setStorageSync('token', token);
        wx.setStorageSync('refreshToken', refreshToken);

        return { success: true };
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('刷新Token失败:', error);
      throw error;
    }
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  // 隐藏加载中
  hideLoading() {
    wx.hideLoading();
  },

  // 显示提示
  showToast(title, icon = 'success') {
    wx.showToast({
      title,
      icon,
      duration: 2000
    });
  }
});
