// generate-icons.js - 生成简单的占位图标
// 这个脚本可以在Node.js环境中运行，生成基本的PNG图标

const fs = require('fs');
const path = require('path');

// 简单的SVG到PNG转换（需要安装相关依赖）
// npm install canvas

try {
  const { createCanvas } = require('canvas');
  
  // 生成简单的图标
  function generateIcon(text, size, color, bgColor = 'transparent') {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // 设置背景
    if (bgColor !== 'transparent') {
      ctx.fillStyle = bgColor;
      ctx.fillRect(0, 0, size, size);
    }
    
    // 设置文字
    ctx.fillStyle = color;
    ctx.font = `${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, size / 2, size / 2);
    
    return canvas.toBuffer('image/png');
  }
  
  // TabBar图标配置
  const tabBarIcons = [
    { name: 'home', text: '🏠', activeText: '🏠' },
    { name: 'equipment', text: '⚙️', activeText: '⚙️' },
    { name: 'orders', text: '📋', activeText: '📋' },
    { name: 'chat', text: '💬', activeText: '💬' },
    { name: 'profile', text: '👤', activeText: '👤' }
  ];
  
  // 生成TabBar图标
  tabBarIcons.forEach(icon => {
    // 未选中状态 - 灰色
    const normalIcon = generateIcon(icon.text, 81, '#666666');
    fs.writeFileSync(path.join(__dirname, `${icon.name}.png`), normalIcon);
    
    // 选中状态 - 蓝色
    const activeIcon = generateIcon(icon.activeText, 81, '#1976D2');
    fs.writeFileSync(path.join(__dirname, `${icon.name}_active.png`), activeIcon);
  });
  
  // 生成其他常用图标
  const otherIcons = [
    { name: 'logo', text: '资', size: 120, color: '#1976D2' },
    { name: 'default-avatar', text: '👤', size: 80, color: '#666666' },
    { name: 'scan', text: '📷', size: 32, color: '#666666' },
    { name: 'filter', text: '🔍', size: 32, color: '#666666' },
    { name: 'edit', text: '✏️', size: 32, color: '#666666' },
    { name: 'wechat', text: '💬', size: 40, color: '#07C160' },
    { name: 'add-equipment', text: '➕', size: 48, color: '#1976D2' },
    { name: 'create-order', text: '📝', size: 48, color: '#1976D2' },
    { name: 'scan-qr', text: '📱', size: 48, color: '#1976D2' },
    { name: 'view-orders', text: '📋', size: 48, color: '#1976D2' }
  ];
  
  otherIcons.forEach(icon => {
    const iconBuffer = generateIcon(icon.text, icon.size, icon.color);
    fs.writeFileSync(path.join(__dirname, `${icon.name}.png`), iconBuffer);
  });
  
  console.log('图标生成完成！');
  console.log('生成的图标文件：');
  console.log('- TabBar图标：home.png, home_active.png, equipment.png, equipment_active.png, 等');
  console.log('- 功能图标：logo.png, default-avatar.png, scan.png, 等');
  
} catch (error) {
  console.log('Canvas模块未安装，无法生成图标。');
  console.log('请运行以下命令安装依赖：');
  console.log('npm install canvas');
  console.log('');
  console.log('或者手动添加图标文件到 images/ 目录。');
  console.log('参考 images/README.md 了解详细要求。');
}

// 如果没有canvas，创建一个简单的占位文件
if (!fs.existsSync(path.join(__dirname, 'home.png'))) {
  console.log('创建占位文件...');
  
  // 创建一个简单的占位文件（1x1像素的透明PNG）
  const placeholderPNG = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
    0x0B, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
    0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);
  
  const iconFiles = [
    'home.png', 'home_active.png',
    'equipment.png', 'equipment_active.png',
    'orders.png', 'orders_active.png',
    'chat.png', 'chat_active.png',
    'profile.png', 'profile_active.png',
    'logo.png', 'default-avatar.png', 'scan.png', 'filter.png',
    'edit.png', 'wechat.png', 'add-equipment.png', 'create-order.png',
    'scan-qr.png', 'view-orders.png'
  ];
  
  iconFiles.forEach(filename => {
    fs.writeFileSync(path.join(__dirname, filename), placeholderPNG);
  });
  
  console.log('占位图标文件已创建。');
  console.log('请替换为实际的图标文件以获得更好的显示效果。');
}
