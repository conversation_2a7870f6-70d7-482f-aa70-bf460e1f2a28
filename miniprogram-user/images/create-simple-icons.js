// 创建简单的 PNG 图标 - 使用最小化的 PNG 数据

const fs = require('fs');

// 创建一个简单的 24x24 PNG 图标
function createMinimalPNG(color = '#666666') {
  // 这是一个最小的 24x24 透明 PNG 文件的 base64 数据
  // 实际项目中建议使用专业工具转换
  
  const pngHeader = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG 签名
  ]);
  
  const ihdr = Buffer.from([
    0x00, 0x00, 0x00, 0x0D, // IHDR 长度 13
    0x49, 0x48, 0x44, 0x52, // IHDR 标识
    0x00, 0x00, 0x00, 0x18, // 宽度 24
    0x00, 0x00, 0x00, 0x18, // 高度 24
    0x08, 0x06, 0x00, 0x00, 0x00, // 8位深度，RGBA，无压缩，无过滤，无交错
    0xE0, 0x77, 0x3D, 0xF8  // CRC32
  ]);
  
  // 创建一个简单的图像数据 (24x24 RGBA)
  const width = 24;
  const height = 24;
  const bytesPerPixel = 4; // RGBA
  const rowBytes = width * bytesPerPixel + 1; // +1 for filter byte
  
  const imageData = Buffer.alloc(height * rowBytes);
  
  // 填充图像数据
  for (let y = 0; y < height; y++) {
    const rowStart = y * rowBytes;
    imageData[rowStart] = 0; // 过滤器类型 (无过滤)
    
    for (let x = 0; x < width; x++) {
      const pixelStart = rowStart + 1 + x * bytesPerPixel;
      
      // 创建简单的图标形状
      let alpha = 0;
      
      // 根据位置决定是否绘制像素
      if (isIconPixel(x, y, width, height)) {
        alpha = 255;
      }
      
      // 设置像素颜色 (灰色)
      imageData[pixelStart] = 0x66;     // R
      imageData[pixelStart + 1] = 0x66; // G
      imageData[pixelStart + 2] = 0x66; // B
      imageData[pixelStart + 3] = alpha; // A
    }
  }
  
  // 压缩图像数据 (简化版，实际应该使用 zlib)
  const idat = Buffer.concat([
    Buffer.from([0x00, 0x00, 0x00, imageData.length + 6]), // 长度
    Buffer.from([0x49, 0x44, 0x41, 0x54]), // IDAT 标识
    Buffer.from([0x78, 0x9C]), // zlib 头部
    imageData,
    Buffer.from([0x00, 0x00, 0x00, 0x00]) // CRC32 (简化)
  ]);
  
  const iend = Buffer.from([
    0x00, 0x00, 0x00, 0x00, // IEND 长度 0
    0x49, 0x45, 0x4E, 0x44, // IEND 标识
    0xAE, 0x42, 0x60, 0x82  // CRC32
  ]);
  
  return Buffer.concat([pngHeader, ihdr, iend]);
}

// 判断是否应该绘制像素 (创建简单的图标形状)
function isIconPixel(x, y, width, height) {
  const centerX = width / 2;
  const centerY = height / 2;
  
  // 创建一个简单的方形图标
  return x >= 6 && x <= 18 && y >= 6 && y <= 18 && 
         (x === 6 || x === 18 || y === 6 || y === 18);
}

// 创建更简单的占位图标
function createPlaceholderIcon() {
  // 使用一个已知可用的最小 PNG 文件
  return Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
    0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
    0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x18,
    0x08, 0x06, 0x00, 0x00, 0x00, 0xE0, 0x77, 0x3D,
    0xF8, 0x00, 0x00, 0x00, 0x06, 0x49, 0x44, 0x41,
    0x54, 0x78, 0xDA, 0x63, 0x00, 0x01, 0x00, 0x00,
    0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00,
    0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
    0x42, 0x60, 0x82
  ]);
}

// 生成所有图标
function generateIcons() {
  const icons = ['home', 'equipment', 'orders', 'chat', 'profile'];
  
  console.log('生成简单的 PNG 图标...');
  
  icons.forEach(iconName => {
    try {
      const pngData = createPlaceholderIcon();
      
      // 普通状态
      fs.writeFileSync(`${iconName}.png`, pngData);
      
      // 激活状态 (同样的图标，微信小程序会根据选中状态自动处理颜色)
      fs.writeFileSync(`${iconName}_active.png`, pngData);
      
      console.log(`✓ 生成 ${iconName} 图标`);
    } catch (error) {
      console.error(`✗ 生成 ${iconName} 图标失败:`, error.message);
    }
  });
  
  console.log('\n图标生成完成！');
  console.log('注意：这些是简单的占位图标');
  console.log('建议使用专业工具将 SVG 转换为高质量的 PNG 图标');
}

// 执行生成
generateIcons();
