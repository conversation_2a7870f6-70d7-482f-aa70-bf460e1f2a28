// pages/profile/profile.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    currentRole: 'user',
    roles: [],
    isMultiRole: false,
    stats: {
      equipmentCount: 0,
      orderCount: 0,
      completedOrders: 0
    },
    menuItems: [
      {
        id: 'role-manage',
        title: '身份管理',
        icon: '/images/role-manage.png',
        desc: '切换身份角色',
        showBadge: false
      },
      {
        id: 'verify',
        title: '实名认证',
        icon: '/images/verify.png',
        desc: '完善个人信息',
        showBadge: false
      },
      {
        id: 'notifications',
        title: '消息通知',
        icon: '/images/notifications.png',
        desc: '查看系统消息',
        showBadge: true,
        badgeCount: 0
      },
      {
        id: 'settings',
        title: '设置',
        icon: '/images/settings.png',
        desc: '应用设置',
        showBadge: false
      },
      {
        id: 'help',
        title: '帮助与反馈',
        icon: '/images/help.png',
        desc: '使用帮助',
        showBadge: false
      },
      {
        id: 'about',
        title: '关于我们',
        icon: '/images/about.png',
        desc: '版本信息',
        showBadge: false
      }
    ]
  },

  onLoad() {
    this.checkAuth();
    this.loadUserInfo();
    this.loadUserStats();
  },

  onShow() {
    this.updateUserInfo();
    this.loadNotificationCount();
  },

  onPullDownRefresh() {
    Promise.all([
      this.loadUserInfo(),
      this.loadUserStats(),
      this.loadNotificationCount()
    ]).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新用户信息
  updateUserInfo() {
    this.setData({
      userInfo: app.globalData.userInfo,
      currentRole: app.globalData.currentRole,
      roles: app.globalData.roles,
      isMultiRole: app.globalData.isMultiRole
    });

    this.updateMenuItems();
  },

  // 更新菜单项
  updateMenuItems() {
    const menuItems = this.data.menuItems.map(item => {
      if (item.id === 'role-manage') {
        return {
          ...item,
          showBadge: this.data.isMultiRole,
          badgeCount: this.data.roles.length
        };
      }
      if (item.id === 'verify') {
        return {
          ...item,
          showBadge: !this.data.userInfo?.is_verified,
          desc: this.data.userInfo?.is_verified ? '已完成实名认证' : '完善个人信息'
        };
      }
      return item;
    });

    this.setData({ menuItems });
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const result = await app.request({
        url: '/users/profile',
        method: 'GET'
      });

      if (result.success) {
        const userInfo = result.data;

        app.globalData.userInfo = { ...app.globalData.userInfo, ...userInfo };
        wx.setStorageSync('userInfo', app.globalData.userInfo);

        this.setData({ userInfo });
        this.updateMenuItems();
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  // 加载用户统计
  async loadUserStats() {
    try {
      const result = await app.request({
        url: '/users/stats',
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          stats: result.data
        });
      }
    } catch (error) {
      console.error('加载用户统计失败:', error);
    }
  },

  // 加载通知数量
  async loadNotificationCount() {
    try {
      const result = await app.request({
        url: '/notifications/unread-count',
        method: 'GET'
      });

      if (result.success) {
        const unreadCount = result.data.count || 0;

        const menuItems = this.data.menuItems.map(item => {
          if (item.id === 'notifications') {
            return {
              ...item,
              badgeCount: unreadCount
            };
          }
          return item;
        });

        this.setData({ menuItems });
      }
    } catch (error) {
      console.error('加载通知数量失败:', error);
    }
  },

  // 角色切换回调
  onRoleChange(e) {
    const { newRole } = e.detail;
    this.setData({
      currentRole: newRole
    });
    this.loadUserStats();
  },

  // 菜单项点击
  onMenuItemTap(e) {
    const { id } = e.currentTarget.dataset;

    switch (id) {
      case 'role-manage':
        this.onRoleManage();
        break;
      case 'verify':
        this.onVerify();
        break;
      case 'notifications':
        this.onNotifications();
        break;
      case 'settings':
        this.onSettings();
        break;
      case 'help':
        this.onHelp();
        break;
      case 'about':
        this.onAbout();
        break;
      default:
        break;
    }
  }
});