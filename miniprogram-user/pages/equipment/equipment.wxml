<!--pages/equipment/equipment.wxml-->
<view class="equipment-container">
  <!-- 角色切换器 -->
  <role-switcher 
    mode="compact" 
    custom-class="role-switcher-header"
    bind:rolechange="onRoleChange"
  ></role-switcher>

  <!-- 统计卡片 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item" data-status="" bindtap="onStatFilter">
        <text class="stat-number">{{stats.total}}</text>
        <text class="stat-label">总设备</text>
      </view>
      <view class="stat-item" data-status="normal" bindtap="onStatFilter">
        <text class="stat-number text-success">{{stats.normal}}</text>
        <text class="stat-label">正常</text>
      </view>
      <view class="stat-item" data-status="maintenance" bindtap="onStatFilter">
        <text class="stat-number text-warning">{{stats.maintenance}}</text>
        <text class="stat-label">维护中</text>
      </view>
      <view class="stat-item" data-status="fault" bindtap="onStatFilter">
        <text class="stat-number text-danger">{{stats.fault}}</text>
        <text class="stat-label">故障</text>
      </view>
    </view>
  </view>

  <!-- 搜索和操作栏 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-input-wrapper">
        <input 
          class="search-input"
          placeholder="搜索设备名称、编号..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
        />
        <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="onClearSearch">
          <text>×</text>
        </view>
      </view>
      
      <view class="search-actions">
        <picker
          class="filter-picker"
          mode="selector"
          range="{{filterOptions}}"
          range-key="label"
          value="{{filterIndex}}"
          bindchange="onFilterChange"
        >
          <view class="filter-selector">
            <text class="filter-text">{{currentFilterText}}</text>
            <text class="filter-arrow">▼</text>
          </view>
        </picker>

        <view class="scan-btn" bindtap="onScanQR">
          <text class="scan-icon">📱</text>
        </view>
      </view>
    </view>


  </view>

  <!-- 设备列表 -->
  <view class="equipment-list">
    <view 
      class="equipment-item"
      wx:for="{{equipmentList}}"
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="onViewEquipment"
    >
      <view class="equipment-header">
        <view class="equipment-info">
          <text class="equipment-name">{{item.name}}</text>
          <text class="equipment-code">{{item.maintenanceCode}}</text>
        </view>
        <view class="equipment-status {{getStatusClass(item.status)}}">
          {{getStatusText(item.status)}}
        </view>
      </view>
      
      <view class="equipment-details">
        <view class="detail-row" wx:if="{{item.brand}}">
          <text class="detail-label">品牌：</text>
          <text class="detail-value">{{item.brand}}</text>
        </view>
        <view class="detail-row" wx:if="{{item.model}}">
          <text class="detail-label">型号：</text>
          <text class="detail-value">{{item.model}}</text>
        </view>
        <view class="detail-row" wx:if="{{item.location}}">
          <text class="detail-label">位置：</text>
          <text class="detail-value">{{item.location}}</text>
        </view>
      </view>

      <view class="equipment-footer">
        <text class="update-time">更新时间：{{item.updatedAt}}</text>
        
        <view wx:if="{{canManage}}" class="equipment-actions" catchtap="onStopPropagation">
          <view 
            class="action-btn edit"
            data-id="{{item.id}}"
            bindtap="onEditEquipment"
          >
            编辑
          </view>
          <view 
            class="action-btn delete"
            data-id="{{item.id}}"
            data-name="{{item.name}}"
            bindtap="onDeleteEquipment"
          >
            删除
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && equipmentList.length === 0}}">
    <image src="/images/empty-equipment.png" mode="aspectFit"></image>
    <text class="empty-text">{{searchKeyword ? '未找到相关设备' : '暂无设备'}}</text>
    <button 
      wx:if="{{!searchKeyword}}"
      class="btn btn-primary"
      bindtap="onAddEquipment"
    >
      添加第一台设备
    </button>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{loading && equipmentList.length > 0}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!hasMore && equipmentList.length > 0}}">
    <text>没有更多设备了</text>
  </view>

  <!-- 添加按钮 -->
  <view class="fab" bindtap="onAddEquipment">
    <text class="fab-icon">+</text>
  </view>
</view>
