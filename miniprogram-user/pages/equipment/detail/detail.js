// pages/equipment/detail/detail.js
const app = getApp();

Page({
  data: {
    equipmentId: null,
    equipment: null,
    loading: true,
    currentRole: 'user',
    canManage: false,
    canCreateOrder: true,
    orderHistory: [],
    showOrderHistory: false
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => wx.navigateBack(), 1500);
      return;
    }

    this.setData({ equipmentId: id });
    this.checkAuth();
    this.updateRoleInfo();
    this.loadEquipmentDetail();
  },

  onShow() {
    this.updateRoleInfo();
  },

  onPullDownRefresh() {
    this.loadEquipmentDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    const canManage = ['pi', 'admin', 'super_admin'].includes(currentRole);

    this.setData({
      currentRole,
      canManage
    });
  },

  // 加载设备详情
  async loadEquipmentDetail() {
    try {
      this.setData({ loading: true });

      const result = await app.request({
        url: `/equipment/${this.data.equipmentId}`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          equipment: result.data
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('加载设备详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载维修历史
  async loadOrderHistory() {
    try {
      const result = await app.request({
        url: `/equipment/${this.data.equipmentId}/orders`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          orderHistory: result.data.list || [],
          showOrderHistory: true
        });
      }
    } catch (error) {
      console.error('加载维修历史失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 创建维修工单
  onCreateOrder() {
    if (!this.data.canCreateOrder) {
      wx.showToast({
        title: '暂无权限',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/orders/create/create?equipmentId=${this.data.equipmentId}`
    });
  },

  // 编辑设备
  onEditEquipment() {
    if (!this.data.canManage) {
      wx.showToast({
        title: '暂无权限',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/equipment/edit/edit?id=${this.data.equipmentId}`
    });
  },

  // 查看维修历史
  onViewHistory() {
    this.loadOrderHistory();
  },

  // 隐藏维修历史
  onHideHistory() {
    this.setData({ showOrderHistory: false });
  },

  // 查看工单详情
  onViewOrder(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/orders/detail/detail?id=${id}`
    });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'normal': '正常',
      'maintenance': '维修中',
      'fault': '故障',
      'retired': '已报废'
    };
    return statusMap[status] || '未知';
  },

  // 获取状态样式
  getStatusClass(status) {
    const classMap = {
      'normal': 'status-normal',
      'maintenance': 'status-maintenance',
      'fault': 'status-fault',
      'retired': 'status-retired'
    };
    return classMap[status] || '';
  },

  // 复制信息
  onCopyInfo(e) {
    const { text } = e.currentTarget.dataset;
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        });
      }
    });
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: [url]
    });
  }
});