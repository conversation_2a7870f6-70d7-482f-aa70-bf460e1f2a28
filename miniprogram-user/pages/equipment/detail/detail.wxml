<!--pages/equipment/detail/detail.wxml-->
<view class="equipment-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 设备详情 -->
  <view wx:else class="detail-content">
    <!-- 设备头部信息 -->
    <view class="equipment-header">
      <view class="equipment-info">
        <text class="equipment-name">{{equipment.name}}</text>
        <text class="equipment-model">{{equipment.model}}</text>
        <view class="status-badge {{getStatusClass(equipment.status)}}">
          {{getStatusText(equipment.status)}}
        </view>
      </view>
      <view class="equipment-actions">
        <button wx:if="{{canCreateOrder}}" class="action-btn primary" bindtap="onCreateOrder">
          创建工单
        </button>
        <button wx:if="{{canManage}}" class="action-btn outline" bindtap="onEditEquipment">
          编辑
        </button>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">序列号</text>
          <view class="info-value" bindtap="onCopyInfo" data-text="{{equipment.serialNumber}}">
            <text>{{equipment.serialNumber}}</text>
            <text class="copy-icon">📋</text>
          </view>
        </view>

        <view class="info-item">
          <text class="info-label">制造商</text>
          <text class="info-value">{{equipment.manufacturer || '未填写'}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">存放位置</text>
          <text class="info-value">{{equipment.location}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">所属部门</text>
          <text class="info-value">{{equipment.department || '未填写'}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">课题组</text>
          <text class="info-value">{{equipment.researchGroup || '未填写'}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">设备分类</text>
          <text class="info-value">{{equipment.category || '未分类'}}</text>
        </view>
      </view>
    </view>

    <!-- 采购信息 -->
    <view class="info-section">
      <view class="section-title">采购信息</view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">购买日期</text>
          <text class="info-value">{{equipment.purchaseDate || '未填写'}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">保修期至</text>
          <text class="info-value">{{equipment.warrantyExpiry || '未填写'}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">采购价格</text>
          <text class="info-value">{{equipment.price ? equipment.price + '元' : '未填写'}}</text>
        </view>

        <view class="info-item">
          <text class="info-label">供应商</text>
          <text class="info-value">{{equipment.supplier || '未填写'}}</text>
        </view>
      </view>
    </view>

    <!-- 详细描述 -->
    <view wx:if="{{equipment.description || equipment.specifications}}" class="info-section">
      <view class="section-title">详细信息</view>

      <view wx:if="{{equipment.description}}" class="description-item">
        <text class="desc-label">设备描述</text>
        <text class="desc-content">{{equipment.description}}</text>
      </view>

      <view wx:if="{{equipment.specifications}}" class="description-item">
        <text class="desc-label">技术规格</text>
        <text class="desc-content">{{equipment.specifications}}</text>
      </view>
    </view>

    <!-- 维修历史 -->
    <view class="info-section">
      <view class="section-header">
        <text class="section-title">维修历史</text>
        <button class="history-btn" bindtap="onViewHistory">查看历史</button>
      </view>
    </view>

    <!-- 操作区域 -->
    <view class="bottom-actions">
      <button class="action-btn-large primary" bindtap="onCreateOrder">
        创建维修工单
      </button>
    </view>
  </view>

  <!-- 维修历史弹窗 -->
  <view wx:if="{{showOrderHistory}}" class="history-modal" bindtap="onHideHistory">
    <view class="history-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">维修历史</text>
        <view class="close-btn" bindtap="onHideHistory">×</view>
      </view>

      <view class="history-list">
        <view wx:if="{{orderHistory.length === 0}}" class="empty-history">
          <text>暂无维修记录</text>
        </view>

        <block wx:else>
          <view class="order-item"
                wx:for="{{orderHistory}}"
                wx:key="id"
                data-id="{{item.id}}"
                bindtap="onViewOrder">
            <view class="order-header">
              <text class="order-title">{{item.title}}</text>
              <view class="order-status {{item.status}}">{{item.statusText}}</view>
            </view>
            <view class="order-info">
              <text class="order-number">工单号：{{item.orderNumber}}</text>
              <text class="order-date">创建时间：{{item.createdAt}}</text>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</view>