// pages/equipment/equipment.js
const app = getApp();

Page({
  data: {
    equipmentList: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 10,
    searchKeyword: '',
    filterStatus: '',
    showFilter: false,
    currentRole: 'user',
    canManage: false,
    stats: {
      total: 0,
      normal: 0,
      maintenance: 0,
      fault: 0
    }
  },

  onLoad() {
    this.checkAuth();
    this.updateRoleInfo();
    this.loadEquipmentList();
  },

  onShow() {
    this.updateRoleInfo();
    // 如果从添加页面返回，刷新列表
    if (this.data.equipmentList.length > 0) {
      this.refreshList();
    }
  },

  onPullDownRefresh() {
    this.refreshList().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    const canManage = ['pi', 'admin', 'super_admin'].includes(currentRole);
    
    this.setData({
      currentRole,
      canManage
    });
  },

  // 角色切换回调
  onRoleChange(newRole) {
    this.updateRoleInfo();
    this.refreshList();
  },

  // 加载设备列表
  async loadEquipmentList(isLoadMore = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const page = isLoadMore ? this.data.currentPage + 1 : 1;
      
      const result = await app.request({
        url: '/equipment',
        method: 'GET',
        data: {
          page,
          limit: this.data.pageSize,
          keyword: this.data.searchKeyword,
          status: this.data.filterStatus
        }
      });

      if (result.success) {
        const { list, pagination, stats } = result.data;
        
        this.setData({
          equipmentList: isLoadMore ? [...this.data.equipmentList, ...list] : list,
          currentPage: page,
          hasMore: page < pagination.totalPages,
          stats: stats || this.data.stats
        });
      }
    } catch (error) {
      console.error('加载设备列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 刷新列表
  async refreshList() {
    this.setData({
      refreshing: true,
      currentPage: 1,
      hasMore: true
    });
    
    await this.loadEquipmentList();
    
    this.setData({ refreshing: false });
  },

  // 加载更多
  loadMore() {
    this.loadEquipmentList(true);
  },

  // 搜索设备
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    
    // 防抖搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.refreshList();
    }, 500);
  },

  // 清空搜索
  onClearSearch() {
    this.setData({ searchKeyword: '' });
    this.refreshList();
  },

  // 显示/隐藏筛选
  onToggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 筛选状态
  onFilterStatus(e) {
    const { status } = e.currentTarget.dataset;
    this.setData({
      filterStatus: status === this.data.filterStatus ? '' : status,
      showFilter: false
    });
    this.refreshList();
  },

  // 统计卡片点击过滤
  onStatFilter(e) {
    const { status } = e.currentTarget.dataset;
    this.setData({
      filterStatus: status,
      showFilter: false
    });
    this.refreshList();
  },

  // 查看设备详情
  onViewEquipment(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/equipment/detail/detail?id=${id}`
    });
  },

  // 添加设备
  onAddEquipment() {
    wx.navigateTo({
      url: '/pages/equipment/add/add'
    });
  },

  // 编辑设备
  onEditEquipment(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/equipment/add/add?id=${id}&mode=edit`
    });
  },

  // 删除设备
  onDeleteEquipment(e) {
    const { id, name } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除设备"${name}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.deleteEquipment(id);
        }
      }
    });
  },

  // 执行删除设备
  async deleteEquipment(id) {
    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });

      const result = await app.request({
        url: `/equipment/${id}`,
        method: 'DELETE'
      });

      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.refreshList();
      }
    } catch (error) {
      console.error('删除设备失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 扫码查看设备
  onScanQR() {
    wx.scanCode({
      success: (res) => {
        this.handleQRResult(res.result);
      },
      fail: (error) => {
        console.error('扫描失败:', error);
        wx.showToast({
          title: '扫描失败',
          icon: 'error'
        });
      }
    });
  },

  // 处理二维码扫描结果
  handleQRResult(result) {
    try {
      const equipmentId = this.extractEquipmentId(result);
      
      if (equipmentId) {
        wx.navigateTo({
          url: `/pages/equipment/detail/detail?id=${equipmentId}`
        });
      } else {
        wx.showToast({
          title: '无效的设备二维码',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('处理二维码失败:', error);
      wx.showToast({
        title: '二维码格式错误',
        icon: 'error'
      });
    }
  },

  // 提取设备ID
  extractEquipmentId(qrResult) {
    if (qrResult.startsWith('equipment:')) {
      return qrResult.replace('equipment:', '');
    } else if (/^\d+$/.test(qrResult)) {
      return qrResult;
    }
    return null;
  },

  // 获取设备状态文本
  getStatusText(status) {
    const statusMap = {
      'normal': '正常',
      'maintenance': '维护中',
      'fault': '故障',
      'retired': '已退役'
    };
    return statusMap[status] || status;
  },

  // 获取设备状态样式类
  getStatusClass(status) {
    const classMap = {
      'normal': 'status-normal',
      'maintenance': 'status-maintenance',
      'fault': 'status-fault',
      'retired': 'status-retired'
    };
    return classMap[status] || 'status-normal';
  }
});
