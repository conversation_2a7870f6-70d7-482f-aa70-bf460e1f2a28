/* pages/login/login.wxss */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
}

.login-header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-content {
  width: 100%;
  max-width: 600rpx;
}

.welcome-text {
  text-align: center;
  margin-bottom: 60rpx;
}

.welcome-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12rpx;
}

.welcome-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-actions {
  margin-bottom: 40rpx;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: #ffffff;
  color: #333333;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  border: none;
  margin: 0;
  padding: 0;
}

.login-btn::after {
  border: none;
}

.login-btn:hover {
  background: #f5f5f5;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

.login-btn-old {
  width: 100%;
  height: 88rpx;
  background: #07C160;
  color: #ffffff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  margin: 0;
  padding: 0;
}

.login-btn-old::after {
  border: none;
}

.login-tips {
  text-align: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.tip-text {
  color: rgba(255, 255, 255, 0.7);
}

.tip-link {
  color: #ffffff;
  text-decoration: underline;
}

.login-footer {
  position: absolute;
  bottom: 40rpx;
  text-align: center;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 加载状态 */
.btn-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 响应式适配 */
@media (max-height: 1000rpx) {
  .login-container {
    padding: 20rpx;
  }
  
  .login-header {
    margin-bottom: 60rpx;
  }
  
  .logo {
    width: 100rpx;
    height: 100rpx;
  }
  
  .app-name {
    font-size: 42rpx;
  }
}
