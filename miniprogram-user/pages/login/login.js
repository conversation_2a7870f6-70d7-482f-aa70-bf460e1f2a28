// pages/login/login.js
const app = getApp();

Page({
  data: {
    loading: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo')
  },

  onLoad() {
    // 检查是否已登录
    if (app.globalData.token && app.globalData.userInfo) {
      this.redirectToHome();
    }
  },

  // 微信登录
  async onWechatLogin() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const result = await app.wechatLogin();
      
      if (result.success) {
        if (result.needRoleSelection) {
          // 需要角色选择
          wx.navigateTo({
            url: '/pages/role-select/role-select'
          });
        } else {
          // 直接跳转到首页
          this.redirectToHome();
        }
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 获取用户信息（兼容旧版本）
  onGetUserInfo(e) {
    if (e.detail.userInfo) {
      this.onWechatLogin();
    } else {
      wx.showToast({
        title: '需要授权才能使用',
        icon: 'none'
      });
    }
  }
});
