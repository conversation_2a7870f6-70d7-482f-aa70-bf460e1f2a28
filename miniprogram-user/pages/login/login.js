// pages/login/login.js
const app = getApp();

Page({
  data: {
    loading: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo')
  },

  onLoad() {
    // 检查是否已登录
    if (app.globalData.token && app.globalData.userInfo) {
      this.redirectToHome();
    }
  },

  // 微信登录
  async onWechatLogin() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 先获取用户信息（必须在用户点击事件中同步调用）
      const userProfile = await this.getUserProfile();

      // 然后进行登录
      const result = await app.wechatLogin(userProfile);

      if (result.success) {
        if (result.needRoleSelection) {
          // 需要角色选择
          wx.navigateTo({
            url: '/pages/role-select/role-select'
          });
        } else {
          // 直接跳转到首页
          this.redirectToHome();
        }
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      });
    });
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 获取用户信息（兼容旧版本）
  async onGetUserInfo(e) {
    if (e.detail.userInfo) {
      // 对于旧版本，直接使用 e.detail 中的用户信息
      const userProfile = {
        userInfo: e.detail.userInfo
      };

      if (this.data.loading) return;
      this.setData({ loading: true });

      try {
        const result = await app.wechatLogin(userProfile);

        if (result.success) {
          if (result.needRoleSelection) {
            wx.navigateTo({
              url: '/pages/role-select/role-select'
            });
          } else {
            this.redirectToHome();
          }
        }
      } catch (error) {
        console.error('登录失败:', error);
        wx.showToast({
          title: '登录失败',
          icon: 'error'
        });
      } finally {
        this.setData({ loading: false });
      }
    } else {
      wx.showToast({
        title: '需要授权才能使用',
        icon: 'none'
      });
    }
  }
});
