/* pages/home/<USER>/
.home-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 20rpx;
}

/* 用户头部 */
.user-header {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ffffff;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-role-switcher {
  margin-top: 8rpx;
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 工程师工作状态 */
.work-status-wrapper {
  margin-right: 20rpx;
}

.work-status {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.work-status.available {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.work-status.busy {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
}

.work-status.offline {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
}

.scan-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-btn image {
  width: 32rpx;
  height: 32rpx;
}

/* 统计区域 */
.stats-section {
  margin: -20rpx 30rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stats-grid {
  display: flex;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  border-right: 1rpx solid #f0f0f0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1976D2;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

/* 快捷操作 */
.quick-actions {
  margin: 0 30rpx 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.more-link {
  font-size: 26rpx;
  color: #1976D2;
}

.action-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.action-item {
  flex: 1;
  min-width: calc(50% - 10rpx);
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.action-item image {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
}

.action-item text {
  display: block;
  font-size: 26rpx;
  color: #333333;
}

/* 最近工单 */
.recent-orders {
  margin: 0 30rpx 30rpx;
}

.order-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.order-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item:hover {
  background-color: #f9f9f9;
}

.order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.order-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.order-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-pending {
  background: #FFF3E0;
  color: #F57C00;
}

.status-assigned {
  background: #E3F2FD;
  color: #1976D2;
}

.status-in_progress {
  background: #E8F5E8;
  color: #388E3C;
}

.status-completed {
  background: #E8F5E8;
  color: #388E3C;
}

.status-cancelled {
  background: #FFEBEE;
  color: #D32F2F;
}

.order-content {
  margin-bottom: 12rpx;
}

.equipment-name {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 6rpx;
}

.fault-desc {
  display: block;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}

.order-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.create-time {
  font-size: 22rpx;
  color: #999999;
}

.quick-accept-btn {
  padding: 8rpx 16rpx;
  background: #4CAF50;
  color: #ffffff;
  border-radius: 6rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.quick-accept-btn:hover {
  background: #388E3C;
}

/* 系统公告 */
.announcements {
  margin: 0 30rpx 30rpx;
}

.announcement-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.announcement-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-item:hover {
  background-color: #f9f9f9;
}

.announcement-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.announcement-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

.announcement-time {
  font-size: 22rpx;
  color: #999999;
}

.announcement-content {
  display: block;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: #ffffff;
  margin: 0 30rpx;
  border-radius: 16rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 40rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .user-header {
    padding: 30rpx 20rpx 20rpx;
  }
  
  .stats-section {
    margin: -15rpx 20rpx 20rpx;
  }
  
  .quick-actions,
  .recent-orders,
  .announcements {
    margin: 0 20rpx 20rpx;
  }
  
  .action-item {
    min-width: calc(50% - 10rpx);
    padding: 24rpx 16rpx;
  }
}
