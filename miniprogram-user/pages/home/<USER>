<!--pages/home/<USER>
<view class="home-container">
  <!-- 用户信息和角色切换 -->
  <view class="user-header">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="username">{{userInfo.username || '用户'}}</text>
        <role-switcher mode="compact" custom-class="header-role-switcher" bind:rolechange="onRoleChange"></role-switcher>
      </view>
    </view>
    <view class="header-actions">
      <!-- 工程师工作状态 -->
      <view wx:if="{{currentRole === 'engineer'}}" class="work-status-wrapper">
        <view class="work-status {{workStatus}}" bindtap="onWorkStatusChange" data-status="{{workStatus === 'available' ? 'busy' : 'available'}}">
          <text class="status-text">{{getWorkStatusText(workStatus)}}</text>
        </view>
      </view>
      
      <view class="scan-btn" data-action="scan-qr" bindtap="onQuickAction">
        <image src="/images/scan.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-section">
    <!-- 普通用户/PI统计 -->
    <view wx:if="{{currentRole === 'user' || currentRole === 'pi'}}" class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{stats.equipmentCount}}</text>
        <text class="stat-label">设备数量</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.orderCount}}</text>
        <text class="stat-label">工单总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.pendingOrders}}</text>
        <text class="stat-label">待处理</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.completedOrders}}</text>
        <text class="stat-label">已完成</text>
      </view>
    </view>

    <!-- 工程师统计 -->
    <view wx:if="{{currentRole === 'engineer'}}" class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{stats.assignedOrders}}</text>
        <text class="stat-label">待接单</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.inProgressOrders}}</text>
        <text class="stat-label">进行中</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.completedToday}}</text>
        <text class="stat-label">今日完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.completedOrders}}</text>
        <text class="stat-label">总完成</text>
      </view>
    </view>

    <!-- 管理员统计 -->
    <view wx:if="{{currentRole === 'admin' || currentRole === 'super_admin'}}" class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{stats.totalUsers}}</text>
        <text class="stat-label">用户总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalEquipment}}</text>
        <text class="stat-label">设备总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalOrders}}</text>
        <text class="stat-label">工单总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.todayOrders}}</text>
        <text class="stat-label">今日工单</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="section-title">
      <text>快捷操作</text>
    </view>
    
    <!-- 普通用户/PI操作 -->
    <view wx:if="{{currentRole === 'user' || currentRole === 'pi'}}" class="action-grid">
      <view class="action-item" data-action="add-equipment" bindtap="onQuickAction">
        <image src="/images/add-equipment.png" mode="aspectFit"></image>
        <text>添加设备</text>
      </view>
      <view class="action-item" data-action="create-order" bindtap="onQuickAction">
        <image src="/images/create-order.png" mode="aspectFit"></image>
        <text>报修工单</text>
      </view>
      <view class="action-item" data-action="scan-qr" bindtap="onQuickAction">
        <image src="/images/scan-qr.png" mode="aspectFit"></image>
        <text>扫码查看</text>
      </view>
      <view class="action-item" data-action="view-orders" bindtap="onQuickAction">
        <image src="/images/view-orders.png" mode="aspectFit"></image>
        <text>我的工单</text>
      </view>
    </view>

    <!-- 工程师操作 -->
    <view wx:if="{{currentRole === 'engineer'}}" class="action-grid">
      <view class="action-item" data-action="view-orders" bindtap="onQuickAction">
        <image src="/images/orders.png" mode="aspectFit"></image>
        <text>工单管理</text>
      </view>
      <view class="action-item" data-action="scan-qr" bindtap="onQuickAction">
        <image src="/images/scan-qr.png" mode="aspectFit"></image>
        <text>扫码处理</text>
      </view>
      <view class="action-item" bindtap="onNavigateToChat">
        <image src="/images/chat.png" mode="aspectFit"></image>
        <text>客户沟通</text>
      </view>
      <view class="action-item" bindtap="onNavigateToParts">
        <image src="/images/parts.png" mode="aspectFit"></image>
        <text>配件申请</text>
      </view>
    </view>

    <!-- 管理员操作 -->
    <view wx:if="{{currentRole === 'admin' || currentRole === 'super_admin'}}" class="action-grid">
      <view class="action-item" data-action="view-orders" bindtap="onQuickAction">
        <image src="/images/orders.png" mode="aspectFit"></image>
        <text>工单管理</text>
      </view>
      <view class="action-item" bindtap="onNavigateToUsers">
        <image src="/images/users.png" mode="aspectFit"></image>
        <text>用户管理</text>
      </view>
      <view class="action-item" bindtap="onNavigateToStats">
        <image src="/images/stats.png" mode="aspectFit"></image>
        <text>数据统计</text>
      </view>
      <view class="action-item" bindtap="onNavigateToSettings">
        <image src="/images/settings.png" mode="aspectFit"></image>
        <text>系统设置</text>
      </view>
    </view>
  </view>

  <!-- 最近工单 -->
  <view class="recent-orders" wx:if="{{recentOrders.length > 0}}">
    <view class="section-title">
      <text>{{currentRole === 'engineer' ? '待处理工单' : '最近工单'}}</text>
      <text class="more-link" bindtap="onQuickAction" data-action="view-orders">查看更多</text>
    </view>
    <view class="order-list">
      <view 
        class="order-item"
        wx:for="{{recentOrders}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onViewOrder"
      >
        <view class="order-header">
          <text class="order-number">{{item.orderNumber}}</text>
          <view class="order-status status-{{item.status}}">
            {{getOrderStatusText(item.status)}}
          </view>
        </view>
        <view class="order-content">
          <text class="equipment-name">{{item.equipment.name}}</text>
          <text class="fault-desc">{{item.faultDescription}}</text>
        </view>
        <view class="order-footer">
          <text class="create-time">{{item.createdAt}}</text>
          <!-- 工程师快速接单按钮 -->
          <view 
            wx:if="{{currentRole === 'engineer' && item.status === 'assigned'}}"
            class="quick-accept-btn"
            data-id="{{item.id}}"
            data-order-number="{{item.orderNumber}}"
            bindtap="onQuickAccept"
            catchtap="onStopPropagation"
          >
            快速接单
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 系统公告 -->
  <view class="announcements" wx:if="{{announcements.length > 0}}">
    <view class="section-title">
      <text>系统公告</text>
    </view>
    <view class="announcement-list">
      <view 
        class="announcement-item"
        wx:for="{{announcements}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onViewAnnouncement"
      >
        <view class="announcement-header">
          <text class="announcement-title">{{item.title}}</text>
          <text class="announcement-time">{{item.createdAt}}</text>
        </view>
        <text class="announcement-content">{{item.content}}</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && recentOrders.length === 0}}">
    <image src="/images/empty-orders.png" mode="aspectFit"></image>
    <text class="empty-text">暂无工单记录</text>
    <button 
      wx:if="{{currentRole === 'user' || currentRole === 'pi'}}"
      class="btn btn-primary" 
      data-action="create-order" 
      bindtap="onQuickAction"
    >
      创建第一个工单
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
