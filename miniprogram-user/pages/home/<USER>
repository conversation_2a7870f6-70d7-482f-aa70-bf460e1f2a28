// pages/home/<USER>
const app = getApp();

Page({
  data: {
    userInfo: null,
    currentRole: 'user',
    isMultiRole: false,
    stats: {
      equipmentCount: 0,
      orderCount: 0,
      pendingOrders: 0,
      completedOrders: 0,
      // 工程师专用统计
      assignedOrders: 0,
      inProgressOrders: 0,
      completedToday: 0
    },
    recentOrders: [],
    announcements: [],
    loading: true,
    // 工程师工作状态
    workStatus: 'available' // available, busy, offline
  },

  onLoad() {
    this.checkAuth();
  },

  onShow() {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        currentRole: app.globalData.currentRole,
        isMultiRole: app.globalData.isMultiRole
      });
      this.loadHomeData();
    }
  },

  onPullDownRefresh() {
    this.loadHomeData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }

    this.setData({
      userInfo: app.globalData.userInfo,
      currentRole: app.globalData.currentRole,
      isMultiRole: app.globalData.isMultiRole
    });

    this.loadHomeData();
  },

  // 加载首页数据
  async loadHomeData() {
    try {
      this.setData({ loading: true });

      const [statsResult, ordersResult, announcementsResult] = await Promise.all([
        this.loadStats(),
        this.loadRecentOrders(),
        this.loadAnnouncements()
      ]);

      this.setData({
        stats: statsResult || this.data.stats,
        recentOrders: ordersResult || [],
        announcements: announcementsResult || []
      });

    } catch (error) {
      console.error('加载首页数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      const endpoint = this.data.currentRole === 'engineer' ? '/engineers/stats' : '/users/stats';
      const result = await app.request({
        url: endpoint,
        method: 'GET'
      });

      if (result.success) {
        return result.data;
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
    return null;
  },

  // 加载最近工单
  async loadRecentOrders() {
    try {
      let endpoint = '/orders/my';
      if (this.data.currentRole === 'engineer') {
        endpoint = '/orders/assigned';
      } else if (['admin', 'super_admin'].includes(this.data.currentRole)) {
        endpoint = '/orders';
      }

      const result = await app.request({
        url: endpoint,
        method: 'GET',
        data: {
          page: 1,
          limit: 5
        }
      });

      if (result.success) {
        return result.data.list || [];
      }
    } catch (error) {
      console.error('加载最近工单失败:', error);
    }
    return [];
  },

  // 加载公告
  async loadAnnouncements() {
    try {
      const result = await app.request({
        url: '/announcements',
        method: 'GET',
        data: {
          page: 1,
          limit: 3
        }
      });

      if (result.success) {
        return result.data.list || [];
      }
    } catch (error) {
      console.error('加载公告失败:', error);
    }
    return [];
  },

  // 角色切换回调
  onRoleChange(e) {
    const { newRole } = e.detail;
    this.setData({
      currentRole: newRole
    });
    this.loadHomeData();
  },

  // 工程师工作状态切换
  onWorkStatusChange(e) {
    const { status } = e.currentTarget.dataset;
    this.updateWorkStatus(status);
  },

  // 更新工作状态
  async updateWorkStatus(status) {
    try {
      const result = await app.request({
        url: '/engineers/work-status',
        method: 'POST',
        data: { status }
      });

      if (result.success) {
        this.setData({ workStatus: status });
        wx.showToast({
          title: '状态更新成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('更新工作状态失败:', error);
      wx.showToast({
        title: '状态更新失败',
        icon: 'error'
      });
    }
  },

  // 统计卡片点击
  onStatClick(e) {
    const { filter } = e.currentTarget.dataset;

    if (filter === 'equipment') {
      // 跳转到设备页面
      wx.switchTab({
        url: '/pages/equipment/equipment'
      });
    } else {
      // 跳转到工单页面并设置过滤条件
      const url = `/pages/orders/orders?filter=${filter}`;
      wx.switchTab({
        url: '/pages/orders/orders'
      });

      // 通过事件总线传递过滤条件
      setTimeout(() => {
        const pages = getCurrentPages();
        const ordersPage = pages.find(page => page.route === 'pages/orders/orders');
        if (ordersPage && ordersPage.setFilter) {
          ordersPage.setFilter(filter);
        }
      }, 100);
    }
  },

  // 快捷操作
  onQuickAction(e) {
    const { action } = e.currentTarget.dataset;

    switch (action) {
      case 'add-equipment':
        wx.navigateTo({
          url: '/pages/equipment/add/add'
        });
        break;
      case 'create-order':
        wx.navigateTo({
          url: '/pages/orders/create/create'
        });
        break;
      case 'scan-qr':
        this.onScanQR();
        break;
      case 'view-orders':
        wx.switchTab({
          url: '/pages/orders/orders'
        });
        break;
      case 'accept-order':
        // 工程师快速接单
        this.onQuickAccept(e);
        break;
      default:
        break;
    }
  },

  // 工程师快速接单
  async onQuickAccept(e) {
    const { id, orderNumber } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认接单',
      content: `确定要接收工单 ${orderNumber} 吗？`,
      confirmText: '接单',
      success: (res) => {
        if (res.confirm) {
          this.acceptOrder(id);
        }
      }
    });
  },

  // 执行接单
  async acceptOrder(orderId) {
    try {
      wx.showLoading({
        title: '接单中...',
        mask: true
      });

      const result = await app.request({
        url: `/orders/${orderId}/accept`,
        method: 'POST'
      });

      if (result.success) {
        wx.showToast({
          title: '接单成功',
          icon: 'success'
        });
        this.loadHomeData();
      }
    } catch (error) {
      console.error('接单失败:', error);
      wx.showToast({
        title: '接单失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 扫描二维码
  onScanQR() {
    wx.scanCode({
      success: (res) => {
        console.log('扫描结果:', res);
        this.handleQRResult(res.result);
      },
      fail: (error) => {
        console.error('扫描失败:', error);
        wx.showToast({
          title: '扫描失败',
          icon: 'error'
        });
      }
    });
  },

  // 处理二维码扫描结果
  handleQRResult(result) {
    try {
      if (result.startsWith('equipment:')) {
        const equipmentId = result.replace('equipment:', '');
        wx.navigateTo({
          url: `/pages/equipment/detail/detail?id=${equipmentId}`
        });
      } else if (result.startsWith('order:')) {
        const orderId = result.replace('order:', '');
        wx.navigateTo({
          url: `/pages/orders/detail/detail?id=${orderId}`
        });
      } else if (/^\d+$/.test(result)) {
        // 纯数字，默认为设备ID
        wx.navigateTo({
          url: `/pages/equipment/detail/detail?id=${result}`
        });
      } else {
        wx.showToast({
          title: '无效的二维码',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('处理二维码失败:', error);
      wx.showToast({
        title: '二维码格式错误',
        icon: 'error'
      });
    }
  },

  // 查看工单详情
  onViewOrder(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/orders/detail/detail?id=${id}`
    });
  },

  // 查看公告详情
  onViewAnnouncement(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/announcement/detail?id=${id}`
    });
  },

  // 获取角色显示名称
  getRoleName(role) {
    const roleNames = {
      'user': '普通用户',
      'pi': '课题组负责人',
      'engineer': '工程师',
      'admin': '管理员',
      'super_admin': '超级管理员'
    };
    return roleNames[role] || role;
  },

  // 获取工单状态文本
  getOrderStatusText(status) {
    const statusMap = {
      'pending': '待处理',
      'assigned': '已分配',
      'in_progress': '维修中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  },

  // 获取工作状态文本
  getWorkStatusText(status) {
    const statusMap = {
      'available': '空闲',
      'busy': '忙碌',
      'offline': '离线'
    };
    return statusMap[status] || '空闲';
  }
});
