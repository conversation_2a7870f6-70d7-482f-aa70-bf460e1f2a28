// pages/orders/create/create.js
const app = getApp();

Page({
  data: {
    formData: {
      title: '',
      description: '',
      priority: 'medium',
      equipmentId: '',
      faultType: 'hardware',
      urgentLevel: 'normal',
      contactPhone: '',
      expectedDate: '',
      images: []
    },
    priorities: [
      { value: 'low', label: '低' },
      { value: 'medium', label: '中' },
      { value: 'high', label: '高' },
      { value: 'urgent', label: '紧急' }
    ],
    faultTypes: [
      { value: 'hardware', label: '硬件故障' },
      { value: 'software', label: '软件问题' },
      { value: 'maintenance', label: '定期保养' },
      { value: 'calibration', label: '校准检测' },
      { value: 'other', label: '其他问题' }
    ],
    urgentLevels: [
      { value: 'normal', label: '正常' },
      { value: 'urgent', label: '加急' },
      { value: 'emergency', label: '紧急' }
    ],
    equipment: null,
    loading: false,
    currentRole: 'user'
  },

  onLoad(options) {
    const { equipmentId } = options;
    if (equipmentId) {
      this.setData({
        'formData.equipmentId': equipmentId
      });
      this.loadEquipmentInfo(equipmentId);
    }

    this.checkAuth();
    this.updateRoleInfo();
    this.initUserInfo();
  },

  onShow() {
    this.updateRoleInfo();
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    this.setData({ currentRole });
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = app.globalData.userInfo;
    if (userInfo && userInfo.phone) {
      this.setData({
        'formData.contactPhone': userInfo.phone
      });
    }
  },

  // 加载设备信息
  async loadEquipmentInfo(equipmentId) {
    try {
      const result = await app.request({
        url: `/equipment/${equipmentId}`,
        method: 'GET'
      });

      if (result.success) {
        this.setData({
          equipment: result.data
        });
      }
    } catch (error) {
      console.error('加载设备信息失败:', error);
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择器变化
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const options = this.data[field + 's'];

    this.setData({
      [`formData.${field}`]: options[value].value
    });
  },

  // 日期选择
  onDateChange(e) {
    const { value } = e.detail;
    this.setData({
      'formData.expectedDate': value
    });
  },

  // 选择设备
  onSelectEquipment() {
    wx.navigateTo({
      url: '/pages/equipment/select/select'
    });
  },

  // 上传图片
  onUploadImage() {
    const maxImages = 6;
    const currentCount = this.data.formData.images.length;

    if (currentCount >= maxImages) {
      wx.showToast({
        title: `最多上传${maxImages}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxImages - currentCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        this.uploadImages(tempFilePaths);
      }
    });
  },

  // 上传图片到服务器
  async uploadImages(filePaths) {
    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    try {
      const uploadPromises = filePaths.map(filePath => {
        return new Promise((resolve, reject) => {
          // 模拟上传
          setTimeout(() => {
            resolve(`/images/upload/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`);
          }, 1000);
        });
      });

      const uploadedUrls = await Promise.all(uploadPromises);

      this.setData({
        'formData.images': [...this.data.formData.images, ...uploadedUrls]
      });

      wx.showToast({
        title: '上传成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('上传图片失败:', error);
      wx.showToast({
        title: '上传失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 删除图片
  onDeleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const images = [...this.data.formData.images];
    images.splice(index, 1);

    this.setData({
      'formData.images': images
    });
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: this.data.formData.images
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;

    if (!formData.title.trim()) {
      wx.showToast({
        title: '请填写工单标题',
        icon: 'none'
      });
      return false;
    }

    if (!formData.description.trim()) {
      wx.showToast({
        title: '请描述故障详情',
        icon: 'none'
      });
      return false;
    }

    if (!formData.equipmentId) {
      wx.showToast({
        title: '请选择设备',
        icon: 'none'
      });
      return false;
    }

    if (!formData.contactPhone.trim()) {
      wx.showToast({
        title: '请填写联系电话',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.contactPhone)) {
      wx.showToast({
        title: '请填写正确的手机号',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 提交工单
  async onSubmit() {
    if (!this.validateForm()) return;
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const result = await app.request({
        url: '/orders',
        method: 'POST',
        data: this.data.formData
      });

      if (result.success) {
        wx.showToast({
          title: '工单创建成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('创建工单失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 重置表单
  onReset() {
    wx.showModal({
      title: '确认重置',
      content: '确定要清空所有输入内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              title: '',
              description: '',
              priority: 'medium',
              equipmentId: this.data.formData.equipmentId, // 保留设备ID
              faultType: 'hardware',
              urgentLevel: 'normal',
              contactPhone: app.globalData.userInfo?.phone || '',
              expectedDate: '',
              images: []
            }
          });
        }
      }
    });
  }
});