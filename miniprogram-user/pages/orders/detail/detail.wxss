/* pages/orders/detail/detail.wxss */
.order-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工单头部 */
.order-header {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  padding: 40rpx 30rpx;
  color: #ffffff;
}

.order-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.order-title {
  flex: 1;
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1.4;
  margin-right: 20rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
}

.status-pending {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
}

.status-accepted {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
}

.status-processing {
  background: rgba(156, 39, 176, 0.2);
  color: #9C27B0;
}

.status-completed {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.status-cancelled {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

.order-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  font-size: 26rpx;
  opacity: 0.9;
}

.priority-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.priority-low {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
}

.priority-medium {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
}

.priority-high {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
}

.priority-urgent {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

/* 信息区块 */
.info-section {
  background: #ffffff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-btn {
  padding: 12rpx 24rpx;
  background: #1976D2;
  color: #ffffff;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
}

/* 信息网格 */
.info-grid {
  padding: 20rpx 30rpx 30rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666666;
  width: 200rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
}

.info-value.description {
  line-height: 1.6;
}

.contact-phone {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.phone-icon {
  margin-left: 16rpx;
  font-size: 24rpx;
}

/* 设备卡片 */
.equipment-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #f8f9fa;
  margin: 20rpx 30rpx 30rpx;
  border-radius: 12rpx;
}

.equipment-info {
  flex: 1;
}

.equipment-name {
  display: block;
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.equipment-model {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.equipment-location {
  font-size: 24rpx;
  color: #999999;
}

.view-arrow {
  font-size: 32rpx;
  color: #cccccc;
}

/* 图片画廊 */
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx 30rpx 30rpx;
}

.fault-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
}

/* 操作区域 */
.action-section {
  margin: 20rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.action-btn.primary {
  background: #1976D2;
  color: #ffffff;
}

.action-btn.success {
  background: #4CAF50;
  color: #ffffff;
}

.action-btn.outline {
  background: #ffffff;
  color: #1976D2;
  border: 2rpx solid #1976D2;
}

/* 历史记录弹窗 */
.history-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-content {
  width: 90%;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #666666;
  border-radius: 50%;
  background: #f5f5f5;
}

.history-list {
  max-height: 60vh;
  overflow-y: auto;
}

.empty-history {
  padding: 80rpx;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}

.history-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}

.history-item:last-child {
  border-bottom: none;
}

.history-time {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.history-action {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.history-operator,
.history-remark {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 4rpx;
}