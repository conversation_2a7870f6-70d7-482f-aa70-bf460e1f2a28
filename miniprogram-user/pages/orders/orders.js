// pages/orders/orders.js
const app = getApp();

Page({
  data: {
    orderList: [],
    loading: false,
    refreshing: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 10,
    activeTab: 'all',
    currentRole: 'user',
    canManage: false,
    stats: {
      total: 0,
      pending: 0,
      inProgress: 0,
      completed: 0
    },
    tabs: [
      { key: 'all', name: '全部', count: 0 },
      { key: 'pending', name: '待处理', count: 0 },
      { key: 'in_progress', name: '进行中', count: 0 },
      { key: 'completed', name: '已完成', count: 0 }
    ]
  },

  onLoad() {
    this.checkAuth();
    this.updateRoleInfo();
    this.loadOrderList();
  },

  onShow() {
    this.updateRoleInfo();
    if (this.data.orderList.length > 0) {
      this.refreshList();
    }
  },

  onPullDownRefresh() {
    this.refreshList().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  // 检查登录状态
  checkAuth() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 更新角色信息
  updateRoleInfo() {
    const currentRole = app.globalData.currentRole || 'user';
    const canManage = ['admin', 'super_admin'].includes(currentRole);

    this.setData({
      currentRole,
      canManage
    });

    this.updateTabsByRole(currentRole);
  },

  // 根据角色更新标签页
  updateTabsByRole(role) {
    let tabs = [{ key: 'all', name: '全部', count: 0 }];

    if (role === 'engineer') {
      tabs = [
        { key: 'all', name: '全部', count: 0 },
        { key: 'assigned', name: '待接单', count: 0 },
        { key: 'in_progress', name: '进行中', count: 0 },
        { key: 'completed', name: '已完成', count: 0 }
      ];
    } else if (['admin', 'super_admin'].includes(role)) {
      tabs = [
        { key: 'all', name: '全部', count: 0 },
        { key: 'pending', name: '待分配', count: 0 },
        { key: 'in_progress', name: '进行中', count: 0 },
        { key: 'completed', name: '已完成', count: 0 }
      ];
    } else {
      tabs = [
        { key: 'all', name: '全部', count: 0 },
        { key: 'pending', name: '待处理', count: 0 },
        { key: 'in_progress', name: '进行中', count: 0 },
        { key: 'completed', name: '已完成', count: 0 }
      ];
    }

    this.setData({ tabs });
  },

  // 角色切换回调
  onRoleChange(e) {
    const { newRole } = e.detail;
    this.updateRoleInfo();
    this.setData({ activeTab: 'all' });
    this.refreshList();
  },

  // 切换标签页
  onTabChange(e) {
    const { tab } = e.currentTarget.dataset;
    if (tab === this.data.activeTab) return;

    this.setData({
      activeTab: tab,
      currentPage: 1,
      hasMore: true
    });
    this.loadOrderList();
  },

  // 加载工单列表
  async loadOrderList(isLoadMore = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const page = isLoadMore ? this.data.currentPage + 1 : 1;
      const endpoint = this.getEndpointByRole();

      const result = await app.request({
        url: endpoint,
        method: 'GET',
        data: {
          page,
          limit: this.data.pageSize,
          status: this.data.activeTab === 'all' ? '' : this.data.activeTab
        }
      });

      if (result.success) {
        const { list, pagination, stats } = result.data;

        this.setData({
          orderList: isLoadMore ? [...this.data.orderList, ...list] : list,
          currentPage: page,
          hasMore: page < pagination.totalPages,
          stats: stats || this.data.stats
        });

        this.updateTabCounts(stats);
      }
    } catch (error) {
      console.error('加载工单列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 根据角色获取接口端点
  getEndpointByRole() {
    const role = this.data.currentRole;

    if (role === 'engineer') {
      return '/orders/assigned';
    } else if (['admin', 'super_admin'].includes(role)) {
      return '/orders';
    } else {
      return '/orders/my';
    }
  }
});